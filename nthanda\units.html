<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Units</title>
        <link rel="stylesheet" href="styles.css">
        <script src="axios.js"></script>
        <script src="auth.js"></script>
        <style>
        :root {
            --color-blue: #1c407c;
            --color-yellow: #ffd93d;
            --color-dark-yellow: #e6c235;
            --color-white: #ffffff;
            --color-red: #ff5252;
            --color-green: #4caf50;
            --color-dark-green: #388e3c;
            --color-gray: #F5F5F5;
            --color-text-gray: #666;
            --color-orange: #ff9800;
        }
        
        body {
            background-color: var(--color-yellow);
            margin: 0;
            padding: 0;
            font-family: "Fredoka", sans-serif;
            color: var(--color-blue);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }
        
        .header {
            padding: 16px;
            background-color: var(--color-yellow);
            padding-bottom: 20px;
        }
        
        .header-top {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .back-button {
            background-color: var(--color-blue);
            margin-right: 8px;
            padding: 8px;
            border-radius: 10px;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .back-button img {
            width: 24px;
            height: 24px;
        }
        
        .header-title {
            font-weight: 700;
            font-size: 28px;
            color: var(--color-blue);
            flex: 1;
            margin-left: 16px;
        }
        
        .header-subtitle {
            font-weight: 500;
            font-size: 24px;
            color: var(--color-blue);
            margin-left: 40px;
        }
        
        .content {
            flex: 1;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            background-color: var(--color-white);
            padding: 16px;
            min-height: 80vh;
        }
        
        .progress-card {
            background-color: var(--color-blue);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: var(--color-white);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .progress-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .progress-card-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }
        
        .progress-card-title {
            font-weight: 700;
            font-size: 20px;
        }
        
        .progress-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .progress-stat-item {
            text-align: center;
            flex: 1;
        }
        
        .progress-stat-value {
            font-weight: 700;
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .progress-stat-label {
            font-weight: 400;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .progress-bar-container {
            height: 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            margin-bottom: 8px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background-color: var(--color-yellow);
            border-radius: 6px;
            width: 0%;
            transition: width 1s ease-in-out;
        }
        
        .progress-label {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .quiz-button {
            background-color: var(--color-yellow);
            color: var(--color-blue);
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            border: 2px solid var(--color-dark-yellow);
            border-top-width: 0;
        }
        
        .quiz-button img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }
        
        .section-title {
            font-weight: 600;
            font-size: 20px;
            color: var(--color-blue);
            margin-bottom: 16px;
        }
        
        .units-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .unit-item {
            background-color: var(--color-gray);
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            animation: fadeIn 0.5s ease-in-out;
            text-decoration: none;
            color: inherit;
        }
        
        .unit-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .unit-icon {
            width: 40px;
            height: 40px;
            background-color: var(--color-blue);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-yellow);
            font-weight: 700;
            font-size: 18px;
            margin-right: 12px;
        }
        
        .unit-title-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .unit-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--color-blue);
        }
        
        .unit-description {
            font-weight: 400;
            font-size: 14px;
            color: var(--color-text-gray);
        }
        
        .unit-progress {
            margin-bottom: 12px;
        }
        
        .unit-progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: var(--color-text-gray);
            margin-bottom: 4px;
        }
        
        .unit-progress-bar-container {
            height: 8px;
            background-color: #E0E0E0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .unit-progress-bar {
            height: 100%;
            background-color: var(--color-green);
            border-radius: 4px;
            width: 0%;
            transition: width 1s ease-in-out;
        }
        
        .unit-stats {
            display: flex;
            gap: 16px;
        }
        
        .unit-stat {
            display: flex;
            align-items: center;
        }
        
        .unit-stat-icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
        
        .unit-stat-text {
            font-size: 14px;
            color: var(--color-text-gray);
        }
        
        .completed-badge {
            background-color: var(--color-green);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .waiting-badge {
            background-color: var(--color-orange);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .waiting-message {
            font-size: 14px;
            color: var(--color-orange);
            margin-top: 8px;
            font-weight: 500;
        }
        
        .disabled-unit {
            opacity: 0.7;
            pointer-events: none;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="header-top">
                    <a class="back-button" href="main.html">
                        <img src="assets/icons/arrowleft.png" alt="Back">
                    </a>
                    <h1 class="header-title" id="subjectName"></h1>
                </div>
                <p class="header-subtitle">Select a unit to continue</p>
            </div>

            <div class="content">
                <!-- Progress Card -->
                <div class="progress-card">
                    <div class="progress-stats">
                        <div class="progress-stat-item">
                            <p class="progress-stat-value"
                                id="numberOfUnits">0</p>
                            <p class="progress-stat-label">Units</p>
                        </div>
                        <div class="progress-stat-item">
                            <p class="progress-stat-value"
                                id="numberOfQuizzes">0</p>
                            <p class="progress-stat-label">Quizzes</p>
                        </div>
                        <div class="progress-stat-item">
                            <p class="progress-stat-value" id="quizTaken">0</p>
                            <p class="progress-stat-label">Quiz Taken</p>
                        </div>
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar" id="subjectProgressBar"
                            style="width: 0%"></div>
                    </div>
                    <div
                        style="display: flex; flex-direction: row; justify-content: space-between; margin-bottom: 24px;">
                        <p class="progress-label">Completed Units</p>
                        <p class="progress-percentage"
                            id="progressPercentage">0%</p>
                    </div>

                    <button class="quiz-button"
                        onclick="window.location.href='assessments.html?subject_id=' + getUrlParams('subject_id')">
                        <img src="assets/icons/book.png" alt="Quiz">
                        Assessments
                    </button>
                </div>

                <h3 class="section-title">All units📚</h3>

                <div class="units-container" id="unitsContainer">
                    <!-- Units will be loaded here dynamically -->
                </div>
            </div>
        </div>

        <script>
        // Check if user is logged in
        document.addEventListener('DOMContentLoaded', function() {
            if (!isLoggedIn()) {
                window.location.href = 'login.html';
                return;
            }
            
            const studentDetails = getStudentDetails();
            const subjectId = getUrlParams('subject_id');
            
            // Get subject name
            const subjectName = getSubjectName(subjectId);
            document.getElementById('subjectName').textContent = subjectName;
            
            // Load units for this subject
            loadUnits(subjectId);
        });
        
        function loadUnits(subjectId) {
            const studentDetails = getStudentDetails();
            
            // Make API request to get units
            axios.get(`http://*************:8000/api/units/${subjectId}/${studentDetails.id}`, {
                headers: {
                    'Authorization': `Bearer ${getApiToken()}`
                }
            })
            .then(function(response) {
                const units = response.data.units;
                const unitsContainer = document.getElementById('unitsContainer');
                unitsContainer.innerHTML = '';
                
                // Update progress stats
                updateProgressStats(units);
                localStorage.setItem("units", JSON.stringify(units));
                
                // Render each unit
                units.forEach((unit, index) => {
                    const unitElement = createUnitElement(unit, index);
                    unitsContainer.appendChild(unitElement);
                });
            })
            .catch(function(error) {
                console.error('Error loading units:', error);
                document.getElementById('unitsContainer').innerHTML = 
                    '<div class="error-message">Failed to load units. Please try again later.</div>';
            });
        }
        
        function updateProgressStats(units) {
            const totalUnits = units.length;
            const completedUnits = units.filter(unit => unit.is_complete).length;
            const completedPercentage = totalUnits > 0 ? Math.round((completedUnits / totalUnits) * 100) : 0;
            
            document.getElementById('numberOfUnits').textContent = totalUnits;
            document.getElementById('subjectProgressBar').style.width = `${completedPercentage}%`;
            document.getElementById('progressPercentage').textContent = `${completedPercentage}%`;
            
            // Count quizzes (assessments)
            const totalQuizzes = units.reduce((count, unit) => count + (unit.assessments_count || 0), 0);
            const takenQuizzes = units.reduce((count, unit) => count + (unit.assessments_taken || 0), 0);
            
            document.getElementById('numberOfQuizzes').textContent = totalQuizzes;
            document.getElementById('quizTaken').textContent = takenQuizzes;
        }
        
        function createUnitElement(unit, index) {
            const unitElement = document.createElement('a');
            unitElement.href = `topics.html?unit_id=${unit.id}`;
            unitElement.className = 'unit-item';
            
            // Add disabled class if unit can't be started
            if (!unit.can_start) {
                unitElement.classList.add('disabled-unit');
            }
            
            // Calculate completion percentage
            const totalTopics = unit.topics.length;
            const completedTopics = unit.topics.filter(topic => topic.is_completed).length;
            const completionPercentage = totalTopics > 0 ? Math.round((completedTopics / totalTopics) * 100) : 0;
            
            // Create unit HTML
            unitElement.innerHTML = `
                <div class="unit-header">
                    <div class="unit-icon">${index + 1}</div>
                    <div class="unit-title-container">
                        <h3 class="unit-title">
                            ${unit.name}
                            ${unit.is_complete ? '<span class="completed-badge">Completed</span>' : ''}
                            ${!unit.can_start && !unit.is_complete ? '<span class="waiting-badge">Waiting</span>' : ''}
                        </h3>
                    </div>
                </div>
                <div class="unit-progress">
                    <div class="unit-progress-text">
                        <span>${completedTopics}/${totalTopics} completed</span>
                        <span>${completionPercentage}%</span>
                    </div>
                    <div class="unit-progress-bar-container">
                        <div class="unit-progress-bar" style="width: ${completionPercentage}%"></div>
                    </div>
                </div>
                <div class="unit-stats">
                    <div class="unit-stat">
                        <span class="unit-stat-text">${totalTopics} Topics</span>
                    </div>
                </div>
            `;
            
            // Add waiting message if applicable
            if (!unit.can_start && !unit.is_complete && unit.waiting_time) {
                const waitingDate = new Date(unit.waiting_time);
                const now = new Date();
                const timeDiff = waitingDate - now;
                
                if (timeDiff > 0) {
                    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                    const waitingMessage = document.createElement('div');
                    waitingMessage.className = 'waiting-message';
                    waitingMessage.textContent = `Available in ${hours}h ${minutes}m`;
                    unitElement.appendChild(waitingMessage);
                }
            }
            
            return unitElement;
        }
        
        function getSubjectName(subjectId) {
            const studentDetails = getStudentDetails();
            if (studentDetails && studentDetails.subjects) {
                const subject = studentDetails.subjects.find(s => s.id == subjectId);
                return subject ? subject.name : 'Subject';
            }
            return 'Subject';
        }
        
        function getUrlParams(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }
    </script>
    </body>
</html>