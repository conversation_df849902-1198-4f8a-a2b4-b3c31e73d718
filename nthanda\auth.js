/**
 * Authentication and Session Management
 *
 * This script handles login state checking and redirects based on authentication.
 * It should be included in all pages that require authentication.
 * Works with Laravel backend API for authentication using vanilla JavaScript.
 */

// Check if user is logged in by verifying local storage
function isLoggedIn() {
  return (
    localStorage.getItem("isLogged") === "true" &&
    localStorage.getItem("studentDetails") !== null
  );
}

// Get the current student details
function getStudentDetails() {
  if (!isLoggedIn()) {
    return null;
  }

  try {
    return JSON.parse(localStorage.getItem("studentDetails"));
  } catch (e) {
    console.error("Error parsing student details:", e);
    return null;
  }
}

// Get API token if available
function getApiToken() {
  return localStorage.getItem("apiToken");
}

// Helper function for making authenticated API requests
function makeAuthRequest(url, method, successCallback, errorCallback) {
  const token = getApiToken();

  // Create request
  const xhr = new XMLHttpRequest();
  xhr.open(method, url, true);

  // Set headers
  xhr.setRequestHeader("Content-Type", "application/json");
  xhr.setRequestHeader("Accept", "application/json");

  if (token) {
    xhr.setRequestHeader("Authorization", "Bearer " + token);
  }

  // Handle response
  xhr.onload = function () {
    if (xhr.status >= 200 && xhr.status < 300) {
      // Success
      if (successCallback) {
        try {
          const response = JSON.parse(xhr.responseText);
          successCallback(response);
        } catch (error) {
          console.error("Error parsing JSON response:", error);
          successCallback({});
        }
      }
    } else {
      // Error
      if (xhr.status === 401) {
        // Unauthorized - token expired or invalid
        clearAuthAndRedirect();
        return;
      }

      if (errorCallback) {
        errorCallback(xhr.status, xhr.responseText);
      }
    }
  };

  xhr.onerror = function () {
    if (errorCallback) {
      errorCallback(0, "Network error");
    }
  };

  // Send request
  xhr.send();
}

// Logout function - also calls Laravel logout endpoint if available
function logout() {
  const token = getApiToken();

  // If we have a token, try to logout via API
  if (token) {
    makeAuthRequest(
      "/api/student/logout",
      "POST",
      function () {
        // Success - clear auth and redirect
        clearAuthAndRedirect();
      },
      function () {
        // Error - still clear auth and redirect
        clearAuthAndRedirect();
      }
    );
  } else {
    // No token, just clear local storage and redirect
    clearAuthAndRedirect();
  }
}

// Helper function to clear auth data and redirect
function clearAuthAndRedirect() {
  localStorage.clear();
  window.location.href = "login.html";
}

// Protect page - redirect to login if not authenticated
function protectPage() {
  if (!isLoggedIn()) {
    window.location.href = "login.html";
  }
}

// Check if on login page but already logged in
function redirectIfLoggedIn() {
  const currentPage = window.location.pathname.split("/").pop();

  if (
    isLoggedIn() &&
    (currentPage === "login.html" || currentPage === "index.html")
  ) {
    window.location.href = "main.html";
  }
}

// Function to refresh user data from the server
function refreshUserData() {
  if (!isLoggedIn()) return;

  const token = getApiToken();
  if (!token) return;

  makeAuthRequest("/api/student/me", "GET", function (response) {
    // Update local storage with fresh data
    if (response.success || response.status === "success") {
      const studentData = response.data || response.student || response;
      localStorage.setItem("studentDetails", JSON.stringify(studentData));

      // Update UI with new data
      updateUserInterface(studentData);
    }
  });
}

// Update UI elements with user data
function updateUserInterface(studentData) {
  if (!studentData) return;

  // Update user name if element exists
  const userNameElement = document.querySelector(".user-name");
  const gradeTitleElement = document.querySelector(".greeting");
  if (userNameElement) {
    userNameElement.textContent = studentData.name || "Student 🧒";
  }

  if (gradeTitleElement) {
    gradeTitleElement.textContent = studentData.grade || "🧒";
  }

  // Update user grade if element exists
  const userGradeElement = document.querySelector(".user-grade");
  if (userGradeElement) {
    userGradeElement.textContent = studentData.grade || "🧒";
  }

  // Update avatar if element exists
  const userAvatarElement = document.querySelector(".user-avatar");
  if (userAvatarElement && studentData.avatar) {
    userAvatarElement.src = studentData.avatar;
  }

  // Update progress if element exists
  const progressElement = document.getElementById("progressPercent");
  if (progressElement && studentData.progress) {
    progressElement.textContent = studentData.progress + "%";
  }
}

// Initialize authentication on page load
document.addEventListener("DOMContentLoaded", function () {
  const currentPage = window.location.pathname.split("/").pop();

  // Redirect from login page if already logged in
  if (currentPage === "login.html" || currentPage === "index.html") {
    redirectIfLoggedIn();
  }
  // Protect all other pages except registration
  else if (currentPage !== "register.html") {
    protectPage();

    // If the user is logged in, display their info
    if (isLoggedIn()) {
      const student = getStudentDetails();
      updateUserInterface(student);

      // Optionally refresh user data from server
      refreshUserData();
    }
  }

  // Set up logout button click handler
  const logoutButtons = document.querySelectorAll(".logout-button");
  logoutButtons.forEach(function (button) {
    button.addEventListener("click", function (e) {
      e.preventDefault();
      logout();
    });
  });
});
