import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Head, router, usePage } from '@inertiajs/react';
import { ChevronLeft, Volume2 } from 'lucide-react';
import { useRef } from 'react';

interface ContentBlock {
    id: number;
    topic_id: number;
    type: 'heading' | 'paragraph' | 'image' | 'video' | 'audio' | 'list' | 'listItem' | 'zip';
    content?: string;
    media_path?: string;
    audio_path?: string;
    order: number;
    parent_id?: number;
    attributes: {
        level?: string;
        url?: string;
        video_path?: string;
        items?: string[];
        listType?: 'bullet' | 'numbered';
        index_path?: string;
        [key: string]: unknown;
    };
    children?: ContentBlock[];
    created_at: string;
    updated_at: string;
}

interface Topic {
    id: number;
    name: string;
    description?: string;
    unit_id: number;
    order: number;
    created_at: string;
    updated_at: string;
}

const ContentBlockRenderer = ({ contentBlock }: { contentBlock: ContentBlock }) => {
    const audioRef = useRef<HTMLAudioElement>(null);

    const playAudio = () => {
        if (audioRef.current) {
            audioRef.current.play();
        }
    };

    const renderNarrationButton = () => {
        if (contentBlock.audio_path) {
            return (
                <div className="mt-2">
                    <button onClick={playAudio} className="flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1 text-xs text-blue-600">
                        <Volume2 className="h-3 w-3" /> Play Narration
                    </button>
                    <audio ref={audioRef} src={`/storage/${contentBlock.audio_path}`} className="hidden" />
                </div>
            );
        }
        return null;
    };

    switch (contentBlock.type) {
        case 'heading': {
            const level = contentBlock.attributes?.level || 'h2';

            const renderHeading = () => {
                switch (level) {
                    case 'h1':
                        return <h1 className="text-3xl font-bold">{contentBlock.content}</h1>;
                    case 'h2':
                        return <h2 className="text-2xl font-bold">{contentBlock.content}</h2>;
                    case 'h3':
                        return <h3 className="text-xl font-semibold">{contentBlock.content}</h3>;
                    case 'h4':
                        return <h4 className="text-lg font-semibold">{contentBlock.content}</h4>;
                    case 'h5':
                        return <h5 className="text-base font-medium">{contentBlock.content}</h5>;
                    case 'h6':
                        return <h6 className="text-sm font-medium">{contentBlock.content}</h6>;
                    default:
                        return <h2 className="text-2xl font-bold">{contentBlock.content}</h2>;
                }
            };

            return (
                <div className="mb-6">
                    {renderHeading()}
                    {renderNarrationButton()}
                </div>
            );
        }
        case 'paragraph':
            return (
                <div className="mb-6">
                    <p className="text-base leading-7 text-gray-700">{contentBlock.content}</p>
                    {renderNarrationButton()}
                </div>
            );
        case 'image':
            return (
                <div className="mb-6">
                    {contentBlock.media_path && (
                        <figure>
                            <img
                                src={`/storage/${contentBlock.media_path}`}
                                alt={contentBlock.content || ''}
                                className="mx-auto max-h-96 rounded-lg"
                            />
                            {contentBlock.content && (
                                <figcaption className="mt-2 text-center text-sm text-gray-500">{contentBlock.content}</figcaption>
                            )}
                        </figure>
                    )}
                    {renderNarrationButton()}
                </div>
            );
        case 'video': {
            const videoUrl = contentBlock.attributes?.url as string;
            const videoPath = contentBlock.attributes?.video_path as string;

            return (
                <div className="mb-6">
                    <h3 className="mb-2 text-lg font-medium">{contentBlock.content}</h3>
                    {videoPath && (
                        <video controls className="mx-auto w-full max-w-2xl rounded-lg" src={`/storage/${videoPath}`}>
                            Your browser does not support the video tag.
                        </video>
                    )}
                    {!videoPath && videoUrl && (
                        <div className="relative aspect-video w-full max-w-2xl overflow-hidden rounded-lg">
                            <iframe
                                src={videoUrl.includes('youtube.com') ? videoUrl.replace('watch?v=', 'embed/') : videoUrl}
                                allowFullScreen
                                className="absolute top-0 left-0 h-full w-full"
                                title={contentBlock.content || 'Video'}
                            ></iframe>
                        </div>
                    )}
                </div>
            );
        }
        case 'audio':
            return (
                <div className="mb-6">
                    <h3 className="mb-2 text-lg font-medium">{contentBlock.content}</h3>
                    {contentBlock.audio_path && (
                        <audio controls className="w-full max-w-md" src={`/storage/${contentBlock.audio_path}`}>
                            Your browser does not support the audio element.
                        </audio>
                    )}
                </div>
            );
        case 'list': {
            const items = contentBlock.attributes?.items || [];
            const listType = contentBlock.attributes?.listType || 'bullet';

            return (
                <div className="mb-6">
                    {listType === 'numbered' ? (
                        <ol className="list-decimal pl-5">
                            {items.map((item, index) => (
                                <li key={index} className="mb-1">
                                    {item}
                                </li>
                            ))}
                        </ol>
                    ) : (
                        <ul className="list-disc pl-5">
                            {items.map((item, index) => (
                                <li key={index} className="mb-1">
                                    {item}
                                </li>
                            ))}
                        </ul>
                    )}
                    {renderNarrationButton()}
                </div>
            );
        }
        case 'zip':
            return (
                <div className="mb-6">
                    <h3 className="mb-2 text-lg font-medium">{contentBlock.content || 'Interactive HTML5 Content'}</h3>
                    {contentBlock.attributes?.index_path && (
                        <div className="relative aspect-video w-full overflow-hidden rounded-lg border border-gray-200 bg-white">
                            <iframe
                                src={`/storage/${contentBlock.attributes.index_path}`}
                                className="absolute top-0 left-0 h-full w-full"
                                title="HTML5 Content"
                                sandbox="allow-scripts allow-same-origin"
                            ></iframe>
                        </div>
                    )}
                    {renderNarrationButton()}
                </div>
            );
        default:
            return null;
    }
};

function Preview() {
    const page = usePage();
    const props = page.props as unknown as {
        topic: Topic;
        contentBlocks: ContentBlock[];
        unit: { id: number; name: string };
        subject: { id: number; name: string };
        class: { id: number; name: string };
    };
    const { topic, contentBlocks, unit, subject, class: classObj } = props;

    return (
        <div>
            <Head title={`Preview: ${topic.name}`}>
                <style type="text/css" media="print">{`
                    @page {
                        margin: 1cm;
                    }
                    body {
                        font-size: 12pt;
                        line-height: 1.5;
                    }
                    .no-print {
                        display: none !important;
                    }
                    .print-container {
                        margin: 0;
                        padding: 0;
                        width: 100%;
                    }
                    .print-content {
                        border: none;
                        box-shadow: none;
                        padding: 0;
                    }
                    video, audio, iframe {
                        display: none;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                    }
                `}</style>
            </Head>
            <div className="print-container mx-auto w-full max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
                <div className="no-print mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="icon" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                            <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <div>
                            <p className="text-sm text-gray-500">
                                {classObj?.name} / {subject?.name} / {unit?.name}
                            </p>
                            <h1 className="text-2xl font-bold text-gray-900">{topic.name}</h1>
                        </div>
                    </div>
                    <Button variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))} className="no-print">
                        Back to Editor
                    </Button>
                </div>

                <Separator className="no-print my-6" />

                {/* Print header */}
                <div className="mb-8 hidden print:block">
                    <p className="text-sm text-gray-500">
                        {classObj?.name} / {subject?.name} / {unit?.name}
                    </p>
                    <h1 className="text-3xl font-bold text-gray-900">{topic.name}</h1>
                </div>

                <div className="print-content rounded-lg border bg-white p-6 shadow-sm">
                    <div className="divide-y divide-gray-100">
                        {contentBlocks.length > 0 ? (
                            contentBlocks.map((block) => <ContentBlockRenderer key={block.id} contentBlock={block} />)
                        ) : (
                            <div className="py-12 text-center">
                                <p className="text-gray-500">No content has been added to this lesson yet.</p>
                            </div>
                        )}
                    </div>
                </div>

                <div className="no-print mt-8 text-center">
                    <Button onClick={() => window.print()} variant="outline">
                        Print Lesson
                    </Button>
                </div>
            </div>
        </div>
    );
}

export default Preview;
