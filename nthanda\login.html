<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Login to Learning</title>
        <link rel="stylesheet" href="styles.css">
        <script src="auth.js"></script>
        <script src="./axios.js"></script>
        <style>
            body {
                background-color: var(--color-yellow);
            }
            .welcome-container {
                background-color: var(--color-yellow);
            }
            .welcome-text {
                color: var(--color-blue);
            }
            .sub-text {
                color: var(--color-blue);
            }
            .pin-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                z-index: 1;
                width: 100%;
                max-width: 250px;
            }
            .pin-input {
                width: 100%;
                padding: 15px;
                font-size: 24px;
                text-align: center;
                border-radius: 20px;
                border: 4px solid var(--color-dark-yellow);
                background-color: var(--color-white);
                color: var(--color-blue);
                box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
                margin-bottom: 15px;
                font-family: "Fredoka", sans-serif;
                font-weight: 600;
                letter-spacing: 5px;
            }
            .pin-input:focus {
                outline: none;
                border-color: var(--color-blue);
            }
            .login-button {
                background-color: var(--color-green);
                border-radius: 30px;
                padding: 12px 40px;
                color: var(--color-white);
                font-weight: 700;
                font-size: 20px;
                cursor: pointer;
                width: 100%;
                max-width: 200px;
                text-align: center;
                letter-spacing: 1px;
                border: 4px solid var(--color-dark-green);
                border-left-width: 2px;
                border-right-width: 2px;
                border-top-width: 0;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: transform 0.2s;
                text-decoration: none;
                display: block;
            }
            .login-button:hover {
                transform: translateY(-2px);
            }
            .login-button:active {
                transform: translateY(1px);
            }
            .error-message {
                color: var(--color-red);
                font-weight: 600;
                margin-bottom: 10px;
                text-align: center;
                display: none;
                animation: shake 0.5s ease-in-out;
            }
            .animal-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                width: 100%;
                max-width: 250px;
                height: 90px;
                margin-bottom: 0px;
            }
            .animal {
            
                width: 80px;
                height: 80px;
                transition: all 0.5s ease;
            }
           
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                50% { transform: translateX(10px); }
                75% { transform: translateX(-10px); }
            }
            .success-animation {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.8);
                z-index: 100;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }
            .success-icon {
                font-size: 80px;
                color: var(--color-green);
                margin-bottom: 20px;
            }
            .pin-input.error {
                border-color: var(--color-red);
                animation: shake 0.5s ease-in-out;
            }
        </style>
    </head>
    <body>
        <div class="welcome-container">
            <!-- Corner Images -->
            <img src="assets/welcome/book.png" alt="Book"
                class="corner-image top-left-image">
            <img src="assets/welcome/coin.png" alt="Coin"
                class="corner-image top-right-image">

            <div class="welcome-content">
                <!-- Logo -->
                <img src="assets/logo.png" alt="Logo" class="logo">

                <!-- Main Content -->
                <h1 class="welcome-text">Hello Friend!</h1>

                <p class="sub-text">Please enter your PIN to start learning</p>

                <!-- Animal Images -->

                <!-- PIN Input -->
                <div class="pin-container">
                    <input type="password" class="pin-input" id="pin-input"
                        maxlength="4" placeholder="****">
                    <div class="error-message" id="error-message">Oops! That PIN
                        is not correct. Try again!</div>
                </div>

                <!-- Login Button -->
                <button class="login-button" id="login-button">Let's
                    Go!</button>
            </div>
        </div>

        <!-- Success Animation -->
        <div class="success-animation" id="success-animation">
            <div class="success-icon">🎉</div>
            <h2>Yay! Login Successful!</h2>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Check if already logged in
                if (isLoggedIn()) {
                    window.location.href = 'main.html';
                    return;
                }
                
                // Animate animals
                

                // Focus on PIN input
                const pinInput = document.getElementById('pin-input');
                pinInput.focus();

                // Handle PIN input to only allow numbers
                pinInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });

                // Login button click handler
                const loginButton = document.getElementById('login-button');
                loginButton.addEventListener('click', loginStudent);

                // Enter key press handler
                pinInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loginStudent();
                    }
                });

                function loginStudent(){
                    const pin = document.getElementById('pin-input').value;
                    if (pin.length !== 4) {
                        showError("Please enter a 4-digit PIN");
                        return;
                    }

                    axios.post('http://192.168.1.135:8000/api/student/login', { pin: pin })
                    .then(response => {
                        if (response.data.success) {
                                    // Save to local storage
                                    localStorage.setItem("isLogged", "true");
                                    
                                    // Adjust the student data based on your API response structure
                                    const studentData = response.data.student;
                                    localStorage.setItem("studentDetails", JSON.stringify(studentData));
                                    localStorage.setItem("subjects", JSON.stringify(response.data.subjects));
                                    localStorage.setItem("units", JSON.stringify(response.data.units));
                                    localStorage.setItem("topics", JSON.stringify(response.data.topics));
                                    
                                    // Store API token if provided
                                    if (response.token) {
                                        localStorage.setItem("apiToken", response.token);
                                    }
                                    
                                    // Show success animation
                                    document.getElementById('success-animation').style.display = 'flex';
                                    
                                    // Redirect after short delay
                                    setTimeout(function() {
                                        window.location.href = "main.html";
                                    }, 2500);
                                } else {
                                    showError(response.message || "Incorrect PIN. Please try again!");
                                }
                    })
                    .catch(error => {
                        console.error("Error:", error);
                    });
                }

                

                function showError(message) {
                    const errorElement = document.getElementById('error-message');
                    errorElement.textContent = message;
                    errorElement.style.display = 'block';
                    
                    const pinInput = document.getElementById('pin-input');
                    pinInput.classList.add('error');
                    
                    // Clear error after 3 seconds
                    setTimeout(function() {
                        errorElement.style.display = 'none';
                        pinInput.classList.remove('error');
                    }, 3000);
                }
            });
        </script>
    </body>
</html>