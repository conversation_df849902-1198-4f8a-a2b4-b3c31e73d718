import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { ChevronLeft, Plus, Trash, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface ContentBlock {
    id: number;
    topic_id: number;
    type: 'heading' | 'paragraph' | 'image' | 'video' | 'audio' | 'list' | 'listItem';
    content?: string;
    media_path?: string;
    audio_path?: string;
    order: number;
    parent_id?: number;
    attributes: {
        level?: string;
        url?: string;
        video_path?: string;
        items?: string[];
        listType?: 'bullet' | 'numbered';
        [key: string]: unknown;
    };
    children?: ContentBlock[];
    created_at: string;
    updated_at: string;
}

interface Topic {
    id: number;
    name: string;
    description?: string;
    unit_id: number;
    order: number;
    created_at: string;
    updated_at: string;
}

interface Unit {
    id: number;
    name: string;
    subject_id: number;
    order: number;
    created_at: string;
    updated_at: string;
}

interface Subject {
    id: number;
    name: string;
    class_id: number;
    created_at: string;
    updated_at: string;
}

interface Class {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

interface PageProps {
    contentBlock: ContentBlock;
    topic: Topic;
    unit: Unit;
    subject: Subject;
    class: Class;
    errors: Record<string, string>;
}

function Edit() {
    const { contentBlock, topic, unit, subject, class: classObj } = usePage<unknown>().props as PageProps;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [listItems, setListItems] = useState<string[]>(
        contentBlock.attributes?.items && Array.isArray(contentBlock.attributes.items) ? contentBlock.attributes.items : [''],
    );

    // Audio fields for all content types
    const [narrationAudio, setNarrationAudio] = useState<File | null>(null);
    const [hasExistingAudio, setHasExistingAudio] = useState(!!contentBlock.audio_path);
    const [removeExistingAudio, setRemoveExistingAudio] = useState(false);
    const audioRef = useRef<HTMLAudioElement>(null);

    // Form state management for different content types
    const [headingData, setHeadingData] = useState({
        level: contentBlock.attributes?.level || 'h2',
        content: contentBlock.content || '',
    });

    const [paragraphData, setParagraphData] = useState({
        content: contentBlock.content || '',
    });

    const [imageData, setImageData] = useState({
        alt: contentBlock.content || '',
        media: null as File | null,
        currentMediaPath: contentBlock.media_path,
        removeExistingMedia: false,
    });

    const [videoData, setVideoData] = useState({
        title: contentBlock.content || '',
        url: (contentBlock.attributes?.url as string) || '',
        video: null as File | null,
        currentVideoPath: (contentBlock.attributes?.video_path as string) || '',
        removeExistingVideo: false,
        previewUrl: '',
    });

    const [audioData, setAudioData] = useState({
        title: contentBlock.content || '',
        audio: null as File | null,
        currentAudioPath: contentBlock.audio_path,
        removeExistingAudio: false,
        previewUrl: '',
    });

    const [listData, setListData] = useState({
        listType: (contentBlock.attributes?.listType as 'bullet' | 'numbered') || 'bullet',
    });

    // Create preview URL for video upload
    useEffect(() => {
        if (videoData.video) {
            const url = URL.createObjectURL(videoData.video);
            setVideoData((prev) => ({ ...prev, previewUrl: url }));

            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [videoData.video]);

    // Create preview URL for audio upload
    useEffect(() => {
        if (audioData.audio) {
            const url = URL.createObjectURL(audioData.audio);
            setAudioData((prev) => ({ ...prev, previewUrl: url }));

            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [audioData.audio]);

    // Create preview URL for narration audio upload
    useEffect(() => {
        if (narrationAudio && audioRef.current) {
            const url = URL.createObjectURL(narrationAudio);
            audioRef.current.src = url;

            return () => {
                URL.revokeObjectURL(url);
            };
        }
    }, [narrationAudio]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            // Create FormData for file uploads
            const data = new FormData();
            data.append('_method', 'PUT'); // For Laravel method spoofing

            // Add type-specific data
            switch (contentBlock.type) {
                case 'heading':
                    data.append('content', headingData.content);
                    data.append('attributes', JSON.stringify({ level: headingData.level }));
                    break;
                case 'paragraph':
                    data.append('content', paragraphData.content);
                    break;
                case 'image':
                    data.append('content', imageData.alt);
                    if (imageData.media) {
                        data.append('media', imageData.media);
                    }
                    if (imageData.removeExistingMedia) {
                        data.append('remove_media', '1');
                    }
                    break;
                case 'video':
                    data.append('content', videoData.title);
                    if (videoData.url) {
                        data.append(
                            'attributes',
                            JSON.stringify({
                                url: videoData.url,
                                video_path: videoData.currentVideoPath,
                            }),
                        );
                    }
                    if (videoData.video) {
                        data.append('video', videoData.video);
                    }
                    if (videoData.removeExistingVideo) {
                        data.append('remove_video', '1');
                    }
                    break;
                case 'audio':
                    data.append('content', audioData.title);
                    if (audioData.audio) {
                        data.append('audio', audioData.audio);
                    }
                    if (audioData.removeExistingAudio) {
                        data.append('remove_audio', '1');
                    }
                    break;
                case 'list':
                    data.append('content', '');
                    data.append(
                        'attributes',
                        JSON.stringify({
                            listType: listData.listType,
                            items: listItems.filter((item) => item.trim() !== ''),
                        }),
                    );
                    break;
            }

            // Add narration audio for all types except video and audio
            if (contentBlock.type !== 'video' && contentBlock.type !== 'audio') {
                if (narrationAudio) {
                    data.append('audio', narrationAudio);
                }
                if (removeExistingAudio) {
                    data.append('remove_audio', '1');
                }
            }

            // Send request
            await axios.post(route('content-blocks.update', { content_block: contentBlock.id }), data, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            // Return to content view
            router.visit(route('topics.content', { topic_id: topic.id }));
        } catch (error) {
            console.error('Error updating content block:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const addListItem = () => {
        setListItems([...listItems, '']);
    };

    const updateListItem = (index: number, value: string) => {
        const newItems = [...listItems];
        newItems[index] = value;
        setListItems(newItems);
    };

    const removeListItem = (index: number) => {
        if (listItems.length > 1) {
            const newItems = [...listItems];
            newItems.splice(index, 1);
            setListItems(newItems);
        }
    };

    // Render the optional audio narration section for content types that need it
    const renderNarrationSection = () => {
        if (contentBlock.type === 'video' || contentBlock.type === 'audio') {
            return null;
        }

        return (
            <div className="mb-6 rounded-md border bg-gray-50 p-4">
                <h3 className="text-md mb-3 font-semibold">Optional Audio Narration</h3>

                {hasExistingAudio && !removeExistingAudio && (
                    <div className="mb-4">
                        <label className="mb-2 block text-sm font-medium">Current Audio Narration</label>
                        <div className="relative mb-2 max-w-md">
                            <audio src={`/storage/${contentBlock.audio_path}`} controls className="w-full" />
                            <button
                                type="button"
                                onClick={() => {
                                    setRemoveExistingAudio(true);
                                    setHasExistingAudio(false);
                                }}
                                className="mt-2 flex items-center text-sm text-red-500"
                            >
                                <Trash className="mr-1 h-4 w-4" /> Remove audio
                            </button>
                        </div>
                    </div>
                )}

                <div className="space-y-2">
                    <label className="block text-sm font-medium">Upload Audio Narration</label>
                    <Input
                        type="file"
                        accept="audio/*"
                        onChange={(e) => {
                            const file = e.target.files?.[0] || null;
                            setNarrationAudio(file);
                        }}
                    />
                    <p className="text-xs text-gray-500">Upload audio narration to assist users who prefer listening.</p>

                    {narrationAudio && (
                        <div className="mt-2">
                            <label className="mb-1 block text-sm font-medium">Preview:</label>
                            <audio ref={audioRef} controls className="w-full max-w-md" />
                        </div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <div>
            <Head title={`Edit ${contentBlock.type} - ${topic.name}`} />
            <div className="mx-auto w-full max-w-5xl px-4 py-8 sm:px-6 lg:px-8">
                <div className="mb-6 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="icon" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                            <ChevronLeft className="h-5 w-5" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                Edit {contentBlock.type.charAt(0).toUpperCase() + contentBlock.type.slice(1)}
                            </h1>
                            <p className="text-sm text-gray-500">Update this content block in {topic.name}</p>
                        </div>
                    </div>
                </div>

                <Separator className="my-6" />

                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    {/* Heading Form */}
                    {contentBlock.type === 'heading' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Heading Level</label>
                                <select
                                    value={headingData.level}
                                    onChange={(e) => setHeadingData({ ...headingData, level: e.target.value })}
                                    className="w-full rounded-md border border-gray-300 p-2"
                                >
                                    <option value="h1">Heading 1 (Largest)</option>
                                    <option value="h2">Heading 2</option>
                                    <option value="h3">Heading 3</option>
                                    <option value="h4">Heading 4</option>
                                    <option value="h5">Heading 5</option>
                                    <option value="h6">Heading 6 (Smallest)</option>
                                </select>
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Heading Text</label>
                                <Input
                                    value={headingData.content}
                                    onChange={(e) => setHeadingData({ ...headingData, content: e.target.value })}
                                    placeholder="Enter heading text"
                                    required
                                />
                            </div>

                            {renderNarrationSection()}

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}

                    {/* Paragraph Form */}
                    {contentBlock.type === 'paragraph' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Paragraph Text</label>
                                <Textarea
                                    value={paragraphData.content}
                                    onChange={(e) => setParagraphData({ content: e.target.value })}
                                    placeholder="Enter paragraph text"
                                    rows={8}
                                    required
                                />
                            </div>

                            {renderNarrationSection()}

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}

                    {/* Image Form */}
                    {contentBlock.type === 'image' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            {imageData.currentMediaPath && !imageData.removeExistingMedia && (
                                <div className="mb-4">
                                    <label className="mb-2 block text-sm font-medium">Current Image</label>
                                    <div className="relative w-full max-w-xs">
                                        <img
                                            src={`/storage/${imageData.currentMediaPath}`}
                                            alt={imageData.alt}
                                            className="max-h-40 rounded-md border border-gray-200 object-contain"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setImageData({ ...imageData, removeExistingMedia: true })}
                                            className="absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white"
                                        >
                                            <X className="h-4 w-4" />
                                        </button>
                                    </div>
                                </div>
                            )}

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Image</label>
                                {(imageData.removeExistingMedia || !imageData.currentMediaPath) && (
                                    <Input
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => {
                                            const file = e.target.files?.[0] || null;
                                            setImageData({ ...imageData, media: file });
                                        }}
                                        required={!imageData.currentMediaPath}
                                    />
                                )}
                                {imageData.removeExistingMedia && imageData.currentMediaPath && (
                                    <p className="text-sm text-red-500">Current image will be removed</p>
                                )}
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Alt Text</label>
                                <Input
                                    value={imageData.alt}
                                    onChange={(e) => setImageData({ ...imageData, alt: e.target.value })}
                                    placeholder="Describe the image for accessibility"
                                    required
                                />
                            </div>

                            {renderNarrationSection()}

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}

                    {/* Video Form */}
                    {contentBlock.type === 'video' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Video Title</label>
                                <Input
                                    value={videoData.title}
                                    onChange={(e) => setVideoData({ ...videoData, title: e.target.value })}
                                    placeholder="Enter video title"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Upload Video</label>
                                <Input
                                    type="file"
                                    accept="video/*"
                                    onChange={(e) => {
                                        const file = e.target.files?.[0] || null;
                                        setVideoData({ ...videoData, video: file });
                                    }}
                                />
                                <p className="text-xs text-gray-500">Upload your own video file, or use a URL below</p>

                                {videoData.previewUrl && (
                                    <div className="mt-4">
                                        <label className="mb-2 block text-sm font-medium">Video Preview</label>
                                        <video
                                            className="h-auto max-w-full rounded border border-gray-200"
                                            controls
                                            src={videoData.previewUrl}
                                        ></video>
                                    </div>
                                )}

                                {videoData.currentVideoPath && !videoData.removeExistingVideo && !videoData.video && (
                                    <div className="mt-4">
                                        <label className="mb-2 block text-sm font-medium">Current Video</label>
                                        <video
                                            className="h-auto max-w-full rounded border border-gray-200"
                                            controls
                                            src={`/storage/${videoData.currentVideoPath}`}
                                        ></video>
                                        <button
                                            type="button"
                                            onClick={() => setVideoData({ ...videoData, removeExistingVideo: true })}
                                            className="mt-2 flex items-center text-sm text-red-500"
                                        >
                                            <Trash className="mr-1 h-4 w-4" /> Remove video
                                        </button>
                                    </div>
                                )}
                            </div>

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">OR Video URL</label>
                                <Input
                                    value={videoData.url}
                                    onChange={(e) => setVideoData({ ...videoData, url: e.target.value })}
                                    placeholder="Enter YouTube or other video URL"
                                />
                                <p className="text-xs text-gray-500">Either upload a video file above or provide a URL to an external video</p>
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}

                    {/* Audio Form */}
                    {contentBlock.type === 'audio' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            {audioData.currentAudioPath && !audioData.removeExistingAudio && (
                                <div className="mb-4">
                                    <label className="mb-2 block text-sm font-medium">Current Audio</label>
                                    <div className="relative max-w-md">
                                        <audio src={`/storage/${audioData.currentAudioPath}`} controls className="w-full" />
                                        <button
                                            type="button"
                                            onClick={() => setAudioData({ ...audioData, removeExistingAudio: true })}
                                            className="mt-2 flex items-center text-sm text-red-500"
                                        >
                                            <Trash className="mr-1 h-4 w-4" /> Remove audio
                                        </button>
                                    </div>
                                </div>
                            )}

                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Audio Title</label>
                                <Input
                                    value={audioData.title}
                                    onChange={(e) => setAudioData({ ...audioData, title: e.target.value })}
                                    placeholder="Enter audio title"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">Audio File</label>
                                {(audioData.removeExistingAudio || !audioData.currentAudioPath) && (
                                    <Input
                                        type="file"
                                        accept="audio/*"
                                        onChange={(e) => {
                                            const file = e.target.files?.[0] || null;
                                            setAudioData({ ...audioData, audio: file });
                                        }}
                                        required={!audioData.currentAudioPath}
                                    />
                                )}
                                {audioData.previewUrl && (
                                    <div className="mt-2">
                                        <label className="mb-1 block text-sm font-medium">Preview:</label>
                                        <audio src={audioData.previewUrl} controls className="w-full max-w-md" />
                                    </div>
                                )}
                                {audioData.removeExistingAudio && audioData.currentAudioPath && (
                                    <p className="text-sm text-red-500">Current audio will be removed</p>
                                )}
                            </div>
                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}

                    {/* List Form */}
                    {contentBlock.type === 'list' && (
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">List Type</label>
                                <select
                                    value={listData.listType}
                                    onChange={(e) => setListData({ listType: e.target.value as 'bullet' | 'numbered' })}
                                    className="w-full rounded-md border border-gray-300 p-2"
                                >
                                    <option value="bullet">Bullet List</option>
                                    <option value="numbered">Numbered List</option>
                                </select>
                            </div>
                            <div className="space-y-2">
                                <label className="block text-sm font-medium">List Items</label>
                                {listItems.map((item, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                        <Input
                                            value={item}
                                            onChange={(e) => updateListItem(index, e.target.value)}
                                            placeholder={`List item ${index + 1}`}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => removeListItem(index)}
                                            disabled={listItems.length === 1}
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                                <Button type="button" variant="outline" size="sm" onClick={addListItem} className="mt-2">
                                    <Plus className="mr-2 h-4 w-4" /> Add Item
                                </Button>
                            </div>

                            {renderNarrationSection()}

                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={() => router.visit(route('topics.content', { topic_id: topic.id }))}>
                                    Cancel
                                </Button>
                                <Button type="submit" disabled={isSubmitting}>
                                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </form>
                    )}
                </div>
            </div>
        </div>
    );
}

export default Edit;
