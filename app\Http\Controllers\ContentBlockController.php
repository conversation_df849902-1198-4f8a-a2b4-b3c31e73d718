<?php

namespace App\Http\Controllers;

use App\Models\ContentBlock;
use App\Models\Topic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class ContentBlockController extends Controller
{
    /**
     * Store a newly created content block.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'topic_id' => 'required|integer|exists:topics,id',
            'type' => 'required|string|in:heading,paragraph,image,video,audio,list,listItem,zip',
            'content' => 'nullable|string',
            'order' => 'required|integer|min:1',
            'parent_id' => 'nullable|integer|exists:content_blocks,id',
            'attributes' => 'nullable|json',
            'media' => 'nullable|file|max:20480', // 20MB max file size
            'audio' => 'nullable|file|mimes:mp3,wav,ogg|max:10240', // 10MB max audio size
            'video' => 'nullable|file|mimes:mp4,mov,avi,wmv|max:51200', // 50MB max video size
            'zip' => 'nullable|file|mimes:zip|max:102400', // 100MB max zip size
        ]);

        $data = [
            'topic_id' => $validated['topic_id'],
            'type' => $validated['type'],
            'content' => $request->input('content'),
            'order' => $validated['order'],
            'parent_id' => $request->input('parent_id'),
            'attributes' => $request->input('attributes') ? json_decode($request->input('attributes'), true) : null,
        ];

        // Handle zip file upload and extraction
        if ($request->hasFile('zip') && $request->input('type') === 'zip') {
            $zipFile = $request->file('zip');
            $uniqueFolderName = 'content-blocks/zip/' . uniqid('zip_', true);
            $zipPath = $zipFile->store($uniqueFolderName, 'public');

            // Create the extraction directory
            $extractPath = storage_path('app/public/' . $uniqueFolderName);
            if (!file_exists($extractPath)) {
                mkdir($extractPath, 0755, true);
            }

            // Extract the zip file
            $zip = new \ZipArchive;
            if ($zip->open(storage_path('app/public/' . $zipPath)) === TRUE) {
                $zip->extractTo($extractPath);
                $zip->close();

                // Delete the original zip file
                Storage::disk('public')->delete($zipPath);

                // Check if index.html exists in the extracted folder
                if (!file_exists($extractPath . '/index.html')) {
                    // Clean up the extracted files
                    $this->deleteDirectory($extractPath);
                    throw ValidationException::withMessages([
                        'zip' => ['ZIP file must contain an index.html file'],
                    ]);
                }

                // Store the extraction path in media_path and index.html path in attributes
                $data['media_path'] = $uniqueFolderName;
                $data['attributes'] = [
                    'index_path' => $uniqueFolderName . '/index.html'
                ];
            } else {
                throw ValidationException::withMessages([
                    'zip' => ['Failed to extract zip file'],
                ]);
            }
        }

        // Handle media file upload
        if ($request->hasFile('media')) {
            $mediaPath = $request->file('media')->store('content-blocks/media', 'public');
            $data['media_path'] = $mediaPath;
        }

        // Handle audio file upload
        if ($request->hasFile('audio')) {
            $audioPath = $request->file('audio')->store('content-blocks/audio', 'public');
            $data['audio_path'] = $audioPath;
        }

        // Handle video file upload for video-type content blocks
        if ($request->hasFile('video') && $request->input('type') === 'video') {
            $videoPath = $request->file('video')->store('content-blocks/videos', 'public');

            // Store video path in attributes
            $attributes = $data['attributes'] ?? [];
            $attributes['video_path'] = $videoPath;
            $data['attributes'] = $attributes;
        }
        $lesson = ContentBlock::where('topic_id', $request->topic_id)->orderBy('order', 'desc')->first();
        $data['order'] = $lesson ? $lesson->order + 1 : 1;
        $contentBlock = ContentBlock::create($data);

        // Load children if it's a list type
        if ($contentBlock->type === 'list') {
            $contentBlock->load('children');
        }

        return redirect()->back()->with('success', 'Content block created successfully');
    }

    /**
     * Update the specified content block.
     */
    public function update(Request $request, ContentBlock $contentBlock)
    {
        $validated = $request->validate([
            'type' => 'sometimes|required|string|in:heading,paragraph,image,video,audio,list,listItem,zip',
            'content' => 'nullable|string',
            'attributes' => 'nullable|json',
            'media' => 'nullable|file|max:20480', // 20MB max file size
            'audio' => 'nullable|file|mimes:mp3,wav,ogg|max:10240', // 10MB max audio size
            'video' => 'nullable|file|mimes:mp4,mov,avi,wmv|max:51200', // 50MB max video size
            'remove_media' => 'nullable|boolean',
            'remove_audio' => 'nullable|boolean',
            'remove_video' => 'nullable|boolean',
        ]);

        $data = [];

        if ($request->has('type')) {
            $data['type'] = $validated['type'];
        }

        if ($request->has('content')) {
            $data['content'] = $request->input('content');
        }

        if ($request->has('attributes')) {
            $data['attributes'] = json_decode($request->input('attributes'), true);
        }

        // Handle media file upload or removal
        if ($request->hasFile('media')) {
            // Delete old media if exists
            if ($contentBlock->media_path) {
                Storage::disk('public')->delete($contentBlock->media_path);
            }

            $mediaPath = $request->file('media')->store('content-blocks/media', 'public');
            $data['media_path'] = $mediaPath;
        } elseif ($request->boolean('remove_media')) {
            // Remove existing media
            if ($contentBlock->media_path) {
                Storage::disk('public')->delete($contentBlock->media_path);
                $data['media_path'] = null;
            }
        }

        // Handle audio file upload or removal
        if ($request->hasFile('audio')) {
            // Delete old audio if exists
            if ($contentBlock->audio_path) {
                Storage::disk('public')->delete($contentBlock->audio_path);
            }

            $audioPath = $request->file('audio')->store('content-blocks/audio', 'public');
            $data['audio_path'] = $audioPath;
        } elseif ($request->boolean('remove_audio')) {
            // Remove existing audio
            if ($contentBlock->audio_path) {
                Storage::disk('public')->delete($contentBlock->audio_path);
                $data['audio_path'] = null;
            }
        }

        // Handle video file upload or removal for video-type content blocks
        if ($contentBlock->type === 'video') {
            $attributes = $data['attributes'] ?? $contentBlock->attributes;

            if ($request->hasFile('video')) {
                // Delete old video if exists
                if (!empty($attributes['video_path'])) {
                    Storage::disk('public')->delete($attributes['video_path']);
                }

                $videoPath = $request->file('video')->store('content-blocks/videos', 'public');
                $attributes['video_path'] = $videoPath;
                $data['attributes'] = $attributes;
            } elseif ($request->boolean('remove_video')) {
                // Remove existing video
                if (!empty($attributes['video_path'])) {
                    Storage::disk('public')->delete($attributes['video_path']);
                    $attributes['video_path'] = null;
                    $data['attributes'] = $attributes;
                }
            }
        }

        $contentBlock->update($data);

        // Load children if it's a list type
        if ($contentBlock->type === 'list') {
            $contentBlock->load('children');
        }

        return response()->json([
            'message' => 'Content block updated successfully',
            'content_block' => $contentBlock,
        ]);
    }

    /**
     * Remove the specified content block.
     */
    public function destroy(ContentBlock $contentBlock)
    {
        // Delete zip folder and its contents if it's a zip type
        if ($contentBlock->type === 'zip' && $contentBlock->media_path) {
            $zipPath = storage_path('app/public/' . $contentBlock->media_path);
            if (is_dir($zipPath)) {
                $this->deleteDirectory($zipPath);
            }
        }

        // Delete media files if they exist
        if ($contentBlock->media_path) {
            Storage::disk('public')->delete($contentBlock->media_path);
        }

        if ($contentBlock->audio_path) {
            Storage::disk('public')->delete($contentBlock->audio_path);
        }

        // Delete video file if it exists (stored in attributes)
        if ($contentBlock->type === 'video' && !empty($contentBlock->attributes['video_path'])) {
            Storage::disk('public')->delete($contentBlock->attributes['video_path']);
        }

        // Delete children blocks if this is a parent
        if ($contentBlock->type === 'list') {
            $contentBlock->children()->each(function ($child) {
                if ($child->media_path) {
                    Storage::disk('public')->delete($child->media_path);
                }

                if ($child->audio_path) {
                    Storage::disk('public')->delete($child->audio_path);
                }

                // Delete video file if it exists
                if ($child->type === 'video' && !empty($child->attributes['video_path'])) {
                    Storage::disk('public')->delete($child->attributes['video_path']);
                }

                $child->delete();
            });
        }

        $contentBlock->delete();

        // Reorder the remaining blocks for this topic
        $remainingBlocks = ContentBlock::where('topic_id', $contentBlock->topic_id)
            ->whereNull('parent_id')
            ->orderBy('order')
            ->get();

        foreach ($remainingBlocks as $index => $block) {
            $block->update(['order' => $index + 1]);
        }

        return response()->json(['message' => 'Content block deleted successfully']);
    }

    /**
     * Reorder content blocks.
     */
    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'blocks' => 'required|array',
            'blocks.*.id' => 'required|integer|exists:content_blocks,id',
            'blocks.*.order' => 'required|integer|min:1',
            'blocks.*.topic_id' => 'required|integer|exists:topics,id',
        ]);

        foreach ($validated['blocks'] as $blockData) {
            ContentBlock::where('id', $blockData['id'])->update(['order' => $blockData['order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Show the form for editing the specified content block.
     */
    public function edit(ContentBlock $contentBlock)
    {
        $topic = Topic::with('unit.subject.class')->findOrFail($contentBlock->topic_id);
        $unit = $topic->unit;
        $subject = $unit->subject;
        $class = $subject->class;

        return Inertia::render('content-blocks/Edit', [
            'contentBlock' => $contentBlock,
            'topic' => $topic,
            'unit' => $unit,
            'subject' => $subject,
            'class' => $class,
        ]);
    }

    /**
     * Helper function to recursively delete a directory and its contents
     */
    private function deleteDirectory($dir)
    {
        if (!file_exists($dir)) {
            return true;
        }

        if (!is_dir($dir)) {
            return unlink($dir);
        }

        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            if (!$this->deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }

        return rmdir($dir);
    }
}