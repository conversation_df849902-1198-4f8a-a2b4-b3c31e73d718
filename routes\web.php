<?php

use App\Http\Controllers\AssessmentController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\TopicController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\ContentBlockController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::post('dashboard/filter', [DashboardController::class, 'filter'])->name('dashboard.filter');

    Route::resource('users', UserController::class);
    Route::resource('teachers', UserController::class);
    Route::controller(StudentController::class)->group(function () {
        Route::get('students', 'index')->name('students.index');
        Route::post('students', 'store')->name('students.store');
        Route::put('students/{id}', 'update')->name('students.update');
        Route::delete('students/{id}', 'destroy')->name('students.destroy');
    });

    Route::controller(SubjectController::class)->group(function () {
        Route::get('subjects', 'index')->name('subjects.index');
        Route::post('subjects', 'store')->name('subjects.store');
        Route::put('subjects/{id}', 'update')->name('subjects.update');
        Route::delete('subjects/{id}', 'destroy')->name('subjects.destroy');
    });


    Route::controller(UnitController::class)->group(function () {
        Route::get('units/{class_id}', 'index')->name('units.index');
        Route::post('units', 'store')->name('units.store');
        Route::put('units/{unit}', 'update')->name('units.update');
        Route::delete('units/{unit}', 'destroy')->name('units.destroy');
        Route::post('units/reorder', 'reorder')->name('units.reorder');
        Route::post('topics/{topic}/convert-to-lesson', 'convertTopicToLesson')->name('topics.convert');
    });

    // Topics
    Route::controller(TopicController::class)->group(function () {
        Route::get('topics/{class_id}', 'index')->name('topics.index');
        Route::post('topics', 'store')->name('topics.store');
        Route::put('topics/{topic}', 'update')->name('topics.update');
        Route::delete('topics/{topic}', 'destroy')->name('topics.destroy');
        Route::post('topics/reorder', 'reorder')->name('topics.reorder');
        Route::get('topics/{topic_id}/content', 'content')->name('topics.content');
    });

    Route::controller(AssessmentController::class)->group(function () {
        Route::get('assessments/{class_id}', 'index')->name('assessments.index');
        Route::post('assessments', 'store')->name('assessments.store');
        Route::put('assessments/{assessment}', 'update')->name('assessments.update');
        Route::delete('assessments/{assessment}', 'destroy')->name('assessments.destroy');

        // Questions management
        Route::get('assessments/{id}/questions', 'questions')->name('assessments.questions');
        Route::post('assessments/{id}/questions', 'storeQuestion')->name('assessments.questions.store');
        Route::post('assessments/{assessment_id}/questions/{question_id}', 'updateQuestion')->name('assessments.questions.update');
        Route::delete('assessments/{assessment_id}/questions/{question_id}', 'destroyQuestion')->name('assessments.questions.destroy');

        // Results management
        Route::get('assessments/{id}/results', 'results')->name('assessments.results');
        Route::get('assessments/{assessment_id}/results/{result_id}', 'resultDetail')->name('assessments.results.detail');
    });
});

// Content Block Routes
Route::middleware(['auth'])->group(function () {
    Route::post('/content-blocks', [ContentBlockController::class, 'store'])->name('content-blocks.store');
    Route::put('/content-blocks/{content_block}', [ContentBlockController::class, 'update'])->name('content-blocks.update');
    Route::delete('/content-blocks/{content_block}', [ContentBlockController::class, 'destroy'])->name('content-blocks.destroy');
    Route::post('/content-blocks/reorder', [ContentBlockController::class, 'reorder'])->name('content-blocks.reorder');
    Route::get('/content-blocks/{content_block}/edit', [ContentBlockController::class, 'edit'])->name('content-blocks.edit');

    // Topic Content Preview
    Route::get('/topics/{topic_id}/preview', [TopicController::class, 'preview'])->name('topics.preview');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
